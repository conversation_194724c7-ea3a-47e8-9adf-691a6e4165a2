// Marketplace Partnerships Management Page
// Comprehensive partnership management with real-time metrics and TimescaleDB integration

import { defineRoute } from "$fresh/server.ts";
import { Head } from "$fresh/runtime.ts";
import DashboardLayout from "../../components/layout/DashboardLayout.tsx";
import PartnershipManagementDashboard from "../../islands/marketplace/PartnershipManagementDashboard.tsx";
import { AppState } from "../../types/fresh.ts";

interface Partnership {
  id: string;
  initiator_tenant_id: string;
  partner_tenant_id: string;
  partnership_type: string;
  status: string;
  revenue_share_percentage: number;
  commission_rate: number;
  attribution_window_days: number;
  created_at: string;
  activated_at?: string;
  initiator_company?: string;
  partner_company?: string;
  total_revenue?: number;
  total_events?: number;
  conversion_rate?: number;
}

interface PartnershipMetrics {
  total_partnerships: number;
  active_partnerships: number;
  pending_partnerships: number;
  total_revenue_30d: number;
  total_commission_30d: number;
  avg_conversion_rate: number;
  top_performing_partnerships: Partnership[];
}

export default defineRoute<AppState>(async (req, ctx) => {
  const user = ctx.state.user;
  
  // Redirect to login if not authenticated
  if (!user) {
    return new Response("", {
      status: 302,
      headers: { Location: "/auth/login" },
    });
  }

  // Check marketplace access
  if (!user.roles?.includes('marketplace_participant')) {
    return new Response("", {
      status: 302,
      headers: { Location: "/marketplace" },
    });
  }

  try {
    // Fetch partnerships data from API
    const partnershipsResponse = await fetch(`${Deno.env.get("API_BASE_URL") || "http://localhost:8001"}/api/marketplace/partnerships`, {
      headers: {
        'Authorization': `Bearer ${ctx.state.token}`,
        'X-Tenant-ID': user.tenantId,
        'Content-Type': 'application/json'
      }
    });

    let partnerships: Partnership[] = [];
    let metrics: PartnershipMetrics = {
      total_partnerships: 0,
      active_partnerships: 0,
      pending_partnerships: 0,
      total_revenue_30d: 0,
      total_commission_30d: 0,
      avg_conversion_rate: 0,
      top_performing_partnerships: []
    };

    if (partnershipsResponse.ok) {
      const data = await partnershipsResponse.json();
      partnerships = data.partnerships || [];
      
      // Calculate metrics
      metrics = {
        total_partnerships: partnerships.length,
        active_partnerships: partnerships.filter(p => p.status === 'active').length,
        pending_partnerships: partnerships.filter(p => p.status === 'pending').length,
        total_revenue_30d: partnerships.reduce((sum, p) => sum + (p.total_revenue || 0), 0),
        total_commission_30d: partnerships.reduce((sum, p) => sum + ((p.total_revenue || 0) * (p.commission_rate || 0) / 100), 0),
        avg_conversion_rate: partnerships.length > 0 
          ? partnerships.reduce((sum, p) => sum + (p.conversion_rate || 0), 0) / partnerships.length 
          : 0,
        top_performing_partnerships: partnerships
          .filter(p => p.total_revenue && p.total_revenue > 0)
          .sort((a, b) => (b.total_revenue || 0) - (a.total_revenue || 0))
          .slice(0, 5)
      };
    }

    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Partnership Management - Marketplace - E-commerce Analytics</title>
          <meta name="description" content="Manage your business partnerships, track performance, and optimize collaboration opportunities." />
        </Head>
        
        <div class="marketplace-partnerships-page">
          {/* Page Header */}
          <div class="mb-8">
            <div class="flex items-center justify-between">
              <div>
                <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
                  Partnership Management
                </h1>
                <p class="text-gray-600 dark:text-gray-300 mt-2">
                  Manage your business partnerships and track collaboration performance
                </p>
              </div>
              <div class="flex space-x-3">
                <a 
                  href="/marketplace/discover"
                  class="inline-flex items-center px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  Discover Partners
                </a>
                <button 
                  class="inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 transition-colors"
                  onclick="window.dispatchEvent(new CustomEvent('openCreatePartnershipModal'))"
                >
                  <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                  </svg>
                  New Partnership
                </button>
              </div>
            </div>
          </div>

          {/* Metrics Overview */}
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-blue-600 dark:text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Total Partnerships</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">{metrics.total_partnerships}</p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Active Partnerships</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">{metrics.active_partnerships}</p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-purple-600 dark:text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Revenue (30d)</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                    ${(metrics.total_revenue_30d / 1000).toFixed(1)}K
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="w-8 h-8 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                    <svg class="w-4 h-4 text-orange-600 dark:text-orange-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                  </div>
                </div>
                <div class="ml-4">
                  <p class="text-sm font-medium text-gray-600 dark:text-gray-300">Avg. Conversion</p>
                  <p class="text-2xl font-semibold text-gray-900 dark:text-white">
                    {metrics.avg_conversion_rate.toFixed(1)}%
                  </p>
                </div>
              </div>
            </div>
          </div>

          {/* Partnership Management Dashboard */}
          <PartnershipManagementDashboard 
            partnerships={partnerships}
            metrics={metrics}
            user={user}
          />
        </div>
      </DashboardLayout>
    );
  } catch (error) {
    console.error('Error loading partnerships:', error);
    
    return (
      <DashboardLayout user={user} activeSection="marketplace">
        <Head>
          <title>Partnership Management - Marketplace - E-commerce Analytics</title>
        </Head>
        
        <div class="marketplace-partnerships-page">
          <div class="mb-8">
            <h1 class="text-3xl font-bold text-gray-900 dark:text-white">
              Partnership Management
            </h1>
            <p class="text-gray-600 dark:text-gray-300 mt-2">
              Manage your business partnerships and track collaboration performance
            </p>
          </div>

          <div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
            <div class="flex">
              <div class="flex-shrink-0">
                <svg class="h-5 w-5 text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                </svg>
              </div>
              <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800 dark:text-red-200">
                  Unable to load partnerships
                </h3>
                <div class="mt-2 text-sm text-red-700 dark:text-red-300">
                  <p>There was an error loading your partnership data. Please try refreshing the page or contact support if the issue persists.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </DashboardLayout>
    );
  }
});
