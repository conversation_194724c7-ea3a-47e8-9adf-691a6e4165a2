// Marketplace Analytics Dashboard Island
// Advanced analytics with D3.js visualizations and TimescaleDB continuous aggregate data

import { useEffect, useState } from "preact/hooks";
import { useSignal } from "@preact/signals";

interface MarketplaceAnalyticsData {
  partnership_metrics: {
    hour: string;
    partnership_id: string;
    event_count: number;
    total_revenue: number;
    conversion_rate: number;
    unique_customers: number;
  }[];
  network_trends: {
    day: string;
    daily_events: number;
    daily_revenue: number;
    unique_customers: number;
    daily_conversion_rate: number;
  }[];
  tenant_activity: {
    hour: string;
    tenant_id: string;
    direction: string;
    event_count: number;
    total_revenue: number;
    unique_partners: number;
  }[];
  realtime_performance: {
    quarter_hour: string;
    partnership_id: string;
    events_15min: number;
    revenue_15min: number;
    conversion_rate_15min: number;
  }[];
  summary_stats: {
    total_partnerships: number;
    active_partnerships: number;
    total_revenue_7d: number;
    total_events_7d: number;
    avg_conversion_rate: number;
    top_partnerships: any[];
  };
}

interface User {
  id: string;
  tenantId: string;
  firstName: string;
  lastName: string;
  email: string;
  roles: string[];
}

interface MarketplaceAnalyticsDashboardProps {
  analyticsData: MarketplaceAnalyticsData;
  user: User;
}

export default function MarketplaceAnalyticsDashboard({ 
  analyticsData: initialData, 
  user 
}: MarketplaceAnalyticsDashboardProps) {
  const [analyticsData, setAnalyticsData] = useState<MarketplaceAnalyticsData>(initialData);
  const [loading, setLoading] = useState(false);
  const [selectedTimeRange, setSelectedTimeRange] = useState('7d');
  const [selectedMetric, setSelectedMetric] = useState('revenue');
  const [autoRefresh, setAutoRefresh] = useState(true);

  const networkTrendsRef = useSignal<HTMLDivElement | null>(null);
  const realtimeMetricsRef = useSignal<HTMLDivElement | null>(null);

  // Auto-refresh data every 30 seconds
  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(async () => {
      try {
        setLoading(true);
        // Refresh data from API
        const response = await fetch(`/api/marketplace/analytics/refresh?period=${selectedTimeRange}`, {
          headers: {
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const newData = await response.json();
          setAnalyticsData(newData);
        }
      } catch (error) {
        console.error('Error refreshing analytics data:', error);
      } finally {
        setLoading(false);
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, selectedTimeRange]);

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0
    }).format(amount);
  };

  return (
    <div class="marketplace-analytics-dashboard space-y-6">
      {/* Controls */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
          <div class="flex flex-col sm:flex-row sm:items-center space-y-4 sm:space-y-0 sm:space-x-4">
            {/* Time Range */}
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Time Range
              </label>
              <select 
                value={selectedTimeRange}
                onChange={(e) => setSelectedTimeRange((e.target as HTMLSelectElement).value)}
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="24h">Last 24 Hours</option>
                <option value="7d">Last 7 Days</option>
                <option value="30d">Last 30 Days</option>
                <option value="90d">Last 90 Days</option>
              </select>
            </div>

            {/* Metric Selection */}
            <div>
              <label class="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                Primary Metric
              </label>
              <select 
                value={selectedMetric}
                onChange={(e) => setSelectedMetric((e.target as HTMLSelectElement).value)}
                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md"
              >
                <option value="revenue">Revenue</option>
                <option value="events">Events</option>
                <option value="conversion">Conversion Rate</option>
                <option value="customers">Unique Customers</option>
              </select>
            </div>
          </div>

          <div class="flex items-center space-x-4">
            {/* Auto Refresh Toggle */}
            <label class="flex items-center">
              <input
                type="checkbox"
                checked={autoRefresh}
                onChange={(e) => setAutoRefresh((e.target as HTMLInputElement).checked)}
                class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
              />
              <span class="ml-2 text-sm text-gray-700 dark:text-gray-300">Auto-refresh</span>
            </label>

            {/* Loading Indicator */}
            {loading && (
              <div class="flex items-center text-sm text-gray-500 dark:text-gray-400">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Updating...
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Network Trends Chart */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Network Trends
          </h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            TimescaleDB Continuous Aggregate
          </div>
        </div>
        
        <div ref={networkTrendsRef} class="w-full h-80">
          {analyticsData.network_trends.length === 0 ? (
            <div class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
                <p class="mt-2">No network trend data available</p>
                <p class="text-sm">Data will appear as partnerships become active</p>
              </div>
            </div>
          ) : (
            <div class="space-y-4">
              {analyticsData.network_trends.slice(-7).map((trend, index) => (
                <div key={index} class="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div class="flex items-center space-x-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {new Date(trend.day).toLocaleDateString()}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {trend.daily_events} events
                    </div>
                  </div>
                  <div class="flex items-center space-x-4">
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      {formatCurrency(trend.daily_revenue)}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {trend.daily_conversion_rate.toFixed(1)}% conversion
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Real-time Performance */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <div class="flex items-center justify-between mb-4">
          <h3 class="text-lg font-medium text-gray-900 dark:text-white">
            Real-time Performance (15-minute intervals)
          </h3>
          <div class="text-sm text-gray-500 dark:text-gray-400">
            Live data from continuous aggregates
          </div>
        </div>
        
        <div ref={realtimeMetricsRef} class="w-full h-64">
          {analyticsData.realtime_performance.length === 0 ? (
            <div class="flex items-center justify-center h-full text-gray-500 dark:text-gray-400">
              <div class="text-center">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                </svg>
                <p class="mt-2">No real-time data available</p>
                <p class="text-sm">Real-time metrics will appear as events are processed</p>
              </div>
            </div>
          ) : (
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
              {analyticsData.realtime_performance.slice(-8).map((perf, index) => (
                <div key={index} class="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                  <div class="text-xs text-gray-500 dark:text-gray-400 mb-1">
                    {new Date(perf.quarter_hour).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                  </div>
                  <div class="text-lg font-semibold text-gray-900 dark:text-white">
                    {perf.events_15min}
                  </div>
                  <div class="text-sm text-gray-600 dark:text-gray-300">
                    events
                  </div>
                  <div class="text-sm text-green-600 dark:text-green-400 mt-1">
                    {formatCurrency(perf.revenue_15min)}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Top Performing Partnerships */}
      <div class="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
        <h3 class="text-lg font-medium text-gray-900 dark:text-white mb-4">
          Top Performing Partnerships
        </h3>
        
        {analyticsData.summary_stats.top_partnerships.length === 0 ? (
          <div class="text-center py-8 text-gray-500 dark:text-gray-400">
            <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
            </svg>
            <p class="mt-2">No partnership performance data available</p>
            <p class="text-sm">Performance metrics will appear as partnerships generate revenue</p>
          </div>
        ) : (
          <div class="space-y-3">
            {analyticsData.summary_stats.top_partnerships.map((partnership, index) => (
              <div key={partnership.partnership_id} class="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div class="flex items-center space-x-4">
                  <div class="w-8 h-8 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center text-sm font-medium text-blue-600 dark:text-blue-400">
                    #{index + 1}
                  </div>
                  <div>
                    <div class="text-sm font-medium text-gray-900 dark:text-white">
                      Partnership {partnership.partnership_id.slice(0, 8)}
                    </div>
                    <div class="text-sm text-gray-500 dark:text-gray-400">
                      {partnership.total_events} total events
                    </div>
                  </div>
                </div>
                <div class="text-right">
                  <div class="text-lg font-semibold text-gray-900 dark:text-white">
                    {formatCurrency(partnership.total_revenue)}
                  </div>
                  <div class="text-sm text-gray-500 dark:text-gray-400">
                    total revenue
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </div>
    </div>
  );
}
